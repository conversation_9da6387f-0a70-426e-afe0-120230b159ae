# frozen_string_literal: true

module Admin
  # Controller for managing categories within dynamic bundles
  # Handles CRUD operations for bundle categories
  class CategoriesController < ApplicationController
    layout 'bundle_admin'
    
    before_action :authenticate_admin!
    before_action :set_bundle, except: [:search, :products_by_category]
    before_action :set_category, only: [:show, :edit, :update, :destroy, :move_up, :move_down]

    # GET /admin/categories/products/:category_id
    # API endpoint to get products by category ID
    def products_by_category
      category_id = params[:category_id]
      page = params[:page] || 1
      per_page = params[:per_page] || 50

      Rails.logger.info("CATEGORIES API: Getting products for category #{category_id}")

      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        Rails.logger.warn("CATEGORIES API: FLUID_API_TOKEN not configured, using mock data")
        render json: generate_mock_products_response(category_id, page, per_page)
        return
      end

      # Try Fluid API first, fallback to mock data if API fails
      begin
        Rails.logger.info("CATEGORIES API: FLUID_API_TOKEN configured, fetching real products...")

        client = FluidClient.new
        products = client.get_products_by_category(category_id, per_page: per_page.to_i)

        Rails.logger.info("CATEGORIES API: Successfully retrieved #{products&.length || 0} products from Fluid API")

        render json: {
          success: true,
          products: products || [],
          pagination: {
            current_page: page,
            per_page: per_page,
            total_pages: 1,
            total_count: products&.length || 0
          }
        }
        return
      rescue => e
        Rails.logger.warn("CATEGORIES API: Fluid API exception - #{e.message}. Falling back to mock data.")
      end

      # Fallback to mock data
      Rails.logger.info("CATEGORIES API: Using mock data")
      render json: generate_mock_products_response(category_id, page, per_page)
    rescue => e
      Rails.logger.error("CATEGORIES API: Exception - #{e.message}")
      render json: {
        success: false,
        error: e.message,
        products: [],
        pagination: { page: 1, per_page: per_page, total: 0, total_pages: 0 }
      }, status: 500
    end

    # GET /admin/categories/search
    # API endpoint for category search (similar to products)
    def search
      search_term = params[:q] || params[:search] || ''
      page = params[:page] || 1
      per_page = params[:per_page] || 50

      Rails.logger.info("CATEGORIES API: Search called with term: '#{search_term}', page: #{page}")

      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        Rails.logger.warn("CATEGORIES API: FLUID_API_TOKEN not configured, using mock data")
        render json: generate_mock_categories_response(search_term, page, per_page)
        return
      end

      # Try Fluid API first, fallback to mock data if API fails
      begin
        Rails.logger.info("CATEGORIES API: FLUID_API_TOKEN configured, fetching real categories...")

        result = Fluid::CategoriesService.call(
          action: :list,
          page: page.to_i,
          per_page: per_page.to_i
        )

        if result.success?
          Rails.logger.info("CATEGORIES API: Successfully retrieved #{result.data[:categories]&.length || 0} categories from Fluid API")

          render json: {
            success: true,
            categories: result.data[:categories] || [],
            pagination: result.data[:pagination] || {}
          }
          return
        else
          Rails.logger.warn("CATEGORIES API: Fluid API failed - #{result.error}. Falling back to mock data.")
        end
      rescue => e
        Rails.logger.warn("CATEGORIES API: Fluid API exception - #{e.message}. Falling back to mock data.")
      end

      # Fallback to mock data
      Rails.logger.info("CATEGORIES API: Using mock data")
      render json: generate_mock_categories_response(search_term, page, per_page)
    rescue => e
      Rails.logger.error("CATEGORIES API: Exception - #{e.message}")
      render json: {
        success: false,
        error: e.message,
        categories: [],
        pagination: { page: 1, per_page: per_page, total: 0, total_pages: 0 }
      }, status: 500
    end

    # GET /admin/bundles/:bundle_id/categories
    # Lists all categories for a specific bundle
    def index
      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        flash[:error] = "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
        redirect_to admin_bundles_path
        return
      end

      # Fetch categories from the bundle metadata
      result = Fluid::BundlesService.call(
        action: :find,
        bundle_id: @bundle_id
      )

      if result.success?
        bundle_data = result.data[:bundle]
        @categories = bundle_data.dig('metadata', 'categories') || []
      else
        @categories = []
        flash.now[:error] = "Failed to load categories: #{result.error}"
      end
    end

    # GET /admin/bundles/:bundle_id/categories/:id
    # Shows details of a specific category
    def show
      # Category details will be loaded by set_category
    end

    # GET /admin/bundles/:bundle_id/categories/new
    # Shows form for creating a new category
    def new
      @category = {
        categoryName: "",
        selectionQuantity: 1,
        displayOrder: next_display_order,
        products: []
      }
    end

    # POST /admin/bundles/:bundle_id/categories
    # Creates a new category
    def create
      @category = category_params.to_h.merge(
        'categoryId' => generate_category_id,
        'displayOrder' => next_display_order,
        'products' => []
      )

      # Use BundleMetadataService to add category
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :add_category,
        category_data: @category
      )

      if result.success?
        flash[:success] = "Category '#{@category['categoryName']}' created successfully!"
        redirect_to admin_bundle_categories_path(@bundle_id)
      else
        @category = category_params.to_h
        flash.now[:error] = "Failed to create category: #{result.error}"
        render :new, status: :unprocessable_entity
      end
    rescue => e
      @category = category_params.to_h
      flash.now[:error] = "Failed to create category: #{e.message}"
      render :new, status: :unprocessable_entity
    end

    # GET /admin/bundles/:bundle_id/categories/:id/edit
    # Shows form for editing an existing category
    def edit
      # Category will be loaded by set_category
    end

    # PATCH/PUT /admin/bundles/:bundle_id/categories/:id
    # Updates an existing category
    def update
      # Merge existing category data with updates
      updated_category = @category.merge(category_params.to_h)

      # Use BundleMetadataService to update category
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :update_category,
        category_id: @category['categoryId'],
        category_data: updated_category
      )

      if result.success?
        flash[:success] = "Category '#{updated_category['categoryName']}' updated successfully!"
        redirect_to admin_bundle_categories_path(@bundle_id)
      else
        flash.now[:error] = "Failed to update category: #{result.error}"
        render :edit, status: :unprocessable_entity
      end
    rescue => e
      flash.now[:error] = "Failed to update category: #{e.message}"
      render :edit, status: :unprocessable_entity
    end

    # DELETE /admin/bundles/:bundle_id/categories/:id
    # Deletes a category
    def destroy
      category_name = @category['categoryName']

      # Use BundleMetadataService to delete category
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :delete_category,
        category_id: @category['categoryId']
      )

      if result.success?
        flash[:success] = "Category '#{category_name}' deleted successfully!"
      else
        flash[:error] = "Failed to delete category: #{result.error}"
      end

      redirect_to admin_bundle_categories_path(@bundle_id)
    rescue => e
      flash[:error] = "Failed to delete category: #{e.message}"
      redirect_to admin_bundle_categories_path(@bundle_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:id/move_up
    # Moves category up in display order
    def move_up
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :move_category_up,
        category_id: @category['categoryId']
      )

      if result.success?
        flash[:success] = "Category moved up successfully!"
      else
        flash[:error] = "Failed to move category: #{result.error}"
      end

      redirect_to admin_bundle_categories_path(@bundle_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:id/move_down
    # Moves category down in display order
    def move_down
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :move_category_down,
        category_id: @category['categoryId']
      )

      if result.success?
        flash[:success] = "Category moved down successfully!"
      else
        flash[:error] = "Failed to move category: #{result.error}"
      end

      redirect_to admin_bundle_categories_path(@bundle_id)
    end

    private

    # Set bundle ID from params
    def set_bundle
      @bundle_id = params[:bundle_id]

      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        flash[:error] = "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
        redirect_to admin_bundles_path
        return
      end

      # Load bundle data for context
      result = Fluid::BundlesService.call(action: :find, bundle_id: @bundle_id)
      if result.success?
        @bundle = result.data[:bundle]
      else
        flash[:error] = "Bundle not found: #{result.error}"
        redirect_to admin_bundles_path
      end
    end

    # Set category for actions that need it
    def set_category
      category_id = params[:id]

      # Find category in bundle metadata
      categories = @bundle.dig('metadata', 'categories') || []
      @category = categories.find { |cat| cat['categoryId'] == category_id }

      unless @category
        flash[:error] = "Category not found"
        redirect_to admin_bundle_categories_path(@bundle_id)
      end
    end

    # Strong parameters for category creation/update
    def category_params
      params.require(:category).permit(:categoryName, :selectionQuantity, :displayOrder)
    end

    # Generate next display order
    def next_display_order
      categories = @bundle.dig('metadata', 'categories') || []
      return 0 if categories.empty?

      max_order = categories.map { |cat| cat['displayOrder'] || 0 }.max
      max_order + 1
    end

    # Generate unique category ID
    def generate_category_id
      "cat-#{SecureRandom.hex(4)}"
    end

    # Placeholder for admin authentication
    def authenticate_admin!
      return true if Rails.env.development?
      redirect_to root_path unless current_user&.admin?
    end

    # Generate mock categories response for testing
    def generate_mock_categories_response(search_term, page, per_page)
      Rails.logger.info("CATEGORIES API: Generating mock categories response for search: '#{search_term}', page: #{page}")

      # Generate mock categories based on search term
      mock_categories = if search_term.present?
        generate_search_mock_categories(search_term, per_page)
      else
        generate_default_mock_categories(per_page)
      end

      {
        success: true,
        categories: mock_categories,
        pagination: {
          current_page: page,
          per_page: per_page,
          total_pages: 1,
          total_count: mock_categories.size
        }
      }
    end

    def generate_search_mock_categories(search_term, count)
      base_categories = [
        { id: 'search-cat-1', name: "#{search_term.titleize} Category", description: "Category for #{search_term.titleize} products" },
        { id: 'search-cat-2', name: "#{search_term.titleize} Advanced", description: "Advanced #{search_term.titleize} category" },
        { id: 'search-cat-3', name: "#{search_term.titleize} Pro", description: "Professional #{search_term.titleize} category" }
      ]

      base_categories.take(count)
    end

    def generate_default_mock_categories(count)
      base_categories = [
        { id: 'mock-cat-1', name: 'Supplements', description: 'Health and wellness supplements' },
        { id: 'mock-cat-2', name: 'Protein Powders', description: 'Whey and plant-based proteins' },
        { id: 'mock-cat-3', name: 'Vitamins', description: 'Essential vitamins and minerals' },
        { id: 'mock-cat-4', name: 'Pre-Workout', description: 'Energy and performance boosters' },
        { id: 'mock-cat-5', name: 'Recovery', description: 'Post-workout recovery products' }
      ]

      base_categories.take(count)
    end

    # Generate mock products response for testing
    def generate_mock_products_response(category_id, page, per_page)
      Rails.logger.info("CATEGORIES API: Generating mock products response for category: #{category_id}, page: #{page}")

      # Generate mock products based on category
      mock_products = generate_mock_products_for_category(category_id, per_page)

      {
        success: true,
        products: mock_products,
        pagination: {
          current_page: page,
          per_page: per_page,
          total_pages: 1,
          total_count: mock_products.size
        }
      }
    end

    def generate_mock_products_for_category(category_id, count)
      category_name = case category_id
                     when 'mock-cat-1' then 'Supplements'
                     when 'mock-cat-2' then 'Protein Powders'
                     when 'mock-cat-3' then 'Vitamins'
                     when 'mock-cat-4' then 'Pre-Workout'
                     when 'mock-cat-5' then 'Recovery'
                     else 'Products'
                     end

      base_products = [
        { id: "#{category_id}-prod-1", name: "Premium #{category_name} #1", sku: "#{category_id.upcase}-001", price: 29.99 },
        { id: "#{category_id}-prod-2", name: "Advanced #{category_name} #2", sku: "#{category_id.upcase}-002", price: 39.99 },
        { id: "#{category_id}-prod-3", name: "Professional #{category_name} #3", sku: "#{category_id.upcase}-003", price: 49.99 },
        { id: "#{category_id}-prod-4", name: "Elite #{category_name} #4", sku: "#{category_id.upcase}-004", price: 59.99 },
        { id: "#{category_id}-prod-5", name: "Ultimate #{category_name} #5", sku: "#{category_id.upcase}-005", price: 69.99 }
      ]

      base_products.take(count)
    end

  end
end
