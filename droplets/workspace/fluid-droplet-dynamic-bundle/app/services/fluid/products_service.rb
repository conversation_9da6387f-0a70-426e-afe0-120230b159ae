# frozen_string_literal: true

module Fluid
  # Service for interacting with Fluid Products API
  # Handles product search, retrieval, and variant management for dynamic bundles
  #
  # @example Search products
  #   result = ProductsService.call(action: :search, query: "protein powder")
  #   if result.success?
  #     products = result.data[:products]
  #   end
  #
  # @example Get product details
  #   result = ProductsService.call(action: :find, product_id: "12345")
  #
  # @example Get product variants
  #   result = ProductsService.call(action: :variants, product_id: "12345")
  #
  class ProductsService < BaseService
    # Initialize the service with action and parameters
    # @param action [Symbol] the action to perform (:search, :find, :variants, :list, :by_category)
    # @param query [String] search query for products (for :search action)
    # @param product_id [String] product ID (for :find, :variants actions)
    # @param category_id [String] category ID (for :by_category action)
    # @param page [Integer] page number for pagination (default: 1)
    # @param per_page [Integer] items per page (default: 20)
    # @param status [String] product status filter ('active', 'inactive', 'all')
    # @param sort_by [String] sort field ('name', 'price', 'created_at')
    # @param sort_order [String] sort direction ('asc', 'desc')
    # @param price_min [Float] minimum price filter
    # @param price_max [Float] maximum price filter
    # @param tags [Array] array of tags to filter by
    def initialize(
      action:,
      query: nil,
      product_id: nil,
      category_id: nil,
      page: 1,
      per_page: 20,
      status: 'active',
      sort_by: 'name',
      sort_order: 'asc',
      price_min: nil,
      price_max: nil,
      tags: []
    )
      @action = action.to_sym
      @query = query
      @product_id = product_id
      @category_id = category_id
      @page = page.to_i
      @per_page = per_page.to_i
      @status = status
      @sort_by = sort_by
      @sort_order = sort_order
      @price_min = price_min&.to_f
      @price_max = price_max&.to_f
      @tags = Array(tags)
    end

    # Execute the products service action
    # @return [ServiceResult] the result of the operation
    def call
      case @action
      when :search
        search_products
      when :find
        find_product
      when :variants
        get_product_variants
      when :list
        list_products
      when :by_category
        products_by_category
      else
        failure("Invalid action: #{@action}. Supported actions: :search, :find, :variants, :list, :by_category")
      end
    end

    private

    # Search for products by query with advanced filters
    # @return [ServiceResult] search results
    def search_products
      return failure("Query is required for search") if @query.blank?

      Rails.logger.info("PRODUCTS API: Searching products with query: '#{@query}', filters: #{filter_summary}")

      begin
        client = FluidClient.new
        response = client.get("/api/company/v1/products", query: search_params)

        transformed_data = transform_products_response(response)
        success(transformed_data)
      rescue => e
        Rails.logger.error("PRODUCTS API: Search failed: #{e.message}")
        failure("Failed to search products: #{e.message}")
      end
    end

    # Find a specific product by ID
    # @return [ServiceResult] product details
    def find_product
      return failure("Product ID is required") if @product_id.blank?

      Rails.logger.info("PRODUCTS API: Finding product with ID: #{@product_id}")

      begin
        client = FluidClient.new
        response = client.get("/api/company/v1/products/#{@product_id}")

        transformed_data = transform_product_response(response)
        success(transformed_data)
      rescue => e
        Rails.logger.error("PRODUCTS API: Find failed: #{e.message}")
        failure("Failed to find product: #{e.message}")
      end
    end

    # Get variants for a specific product
    # @return [ServiceResult] product variants
    def get_product_variants
      return failure("Product ID is required") if @product_id.blank?

      Rails.logger.info("Getting variants for product ID: #{@product_id}")
      
      result = get("/api/company/v1/products/#{@product_id}/variants")
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_variants_response(result.data)
      success(transformed_data)
    end

    # List all products with pagination and filters
    # @return [ServiceResult] paginated products list
    def list_products
      Rails.logger.info("PRODUCTS API: Listing products - page: #{@page}, per_page: #{@per_page}, filters: #{filter_summary}")

      begin
        client = FluidClient.new
        response = client.get("/api/company/v1/products", query: list_params)

        transformed_data = transform_products_response(response)
        success(transformed_data)
      rescue => e
        Rails.logger.error("PRODUCTS API: List failed: #{e.message}")
        failure("Failed to list products: #{e.message}")
      end
    end

    # Get products by category with filters
    # @return [ServiceResult] category products
    def products_by_category
      return failure("Category ID is required") if @category_id.blank?

      Rails.logger.info("PRODUCTS API: Getting products for category: #{@category_id}, filters: #{filter_summary}")

      begin
        client = FluidClient.new
        response = client.get("/api/company/v1/products", query: category_params)

        transformed_data = transform_products_response(response)
        success(transformed_data)
      rescue => e
        Rails.logger.error("PRODUCTS API: Category products failed: #{e.message}")
        failure("Failed to get products by category: #{e.message}")
      end
    end

    # Build search parameters
    # @return [Hash] search parameters
    def search_params
      {
        q: @query,
        page: @page,
        per_page: @per_page,
        include_variants: true,
        status: "active"
      }
    end

    # Build list parameters
    # @return [Hash] list parameters
    def list_params
      {
        page: @page,
        per_page: @per_page,
        include_variants: true,
        status: "active"
      }
    end

    # Transform products API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_products_response(response_data)
      {
        products: response_data.dig("data") || [],
        pagination: {
          current_page: response_data.dig("meta", "current_page") || @page,
          per_page: response_data.dig("meta", "per_page") || @per_page,
          total_pages: response_data.dig("meta", "total_pages") || 1,
          total_count: response_data.dig("meta", "total_count") || 0
        }
      }
    end

    # Transform single product API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_product_response(response_data)
      {
        product: response_data.dig("data") || response_data
      }
    end

    # Transform variants API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_variants_response(response_data)
      {
        variants: response_data.dig("data") || [],
        product_id: @product_id
      }
    end
  end
end
