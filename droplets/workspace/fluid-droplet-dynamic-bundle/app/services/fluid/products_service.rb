# frozen_string_literal: true

module Fluid
  # Service for interacting with Fluid Products API
  # Handles product search, retrieval, and variant management for dynamic bundles
  #
  # @example Search products
  #   result = ProductsService.call(action: :search, query: "protein powder")
  #   if result.success?
  #     products = result.data[:products]
  #   end
  #
  # @example Get product details
  #   result = ProductsService.call(action: :find, product_id: "12345")
  #
  # @example Get product variants
  #   result = ProductsService.call(action: :variants, product_id: "12345")
  #
  class ProductsService < BaseService
    # Initialize the service with action and parameters
    # @param action [Symbol] the action to perform (:search, :find, :variants, :list)
    # @param query [String] search query for products (for :search action)
    # @param product_id [String] product ID (for :find, :variants actions)

    # @param page [Integer] page number for pagination (default: 1)
    # @param per_page [Integer] items per page (default: 20)
    # @param status [String] product status filter ('active', 'inactive', 'all')
    # @param sort_by [String] sort field ('name', 'price', 'created_at')
    # @param sort_order [String] sort direction ('asc', 'desc')
    # @param price_min [Float] minimum price filter
    # @param price_max [Float] maximum price filter
    # @param tags [Array] array of tags to filter by
    def initialize(
      action:,
      query: nil,
      product_id: nil,

      page: 1,
      per_page: 20,
      status: 'active',
      sort_by: 'name',
      sort_order: 'asc',
      price_min: nil,
      price_max: nil,
      tags: []
    )
      @action = action.to_sym
      @query = query
      @product_id = product_id

      @page = page.to_i
      @per_page = per_page.to_i
      @status = status
      @sort_by = sort_by
      @sort_order = sort_order
      @price_min = price_min&.to_f
      @price_max = price_max&.to_f
      @tags = Array(tags)
    end

    # Execute the products service action
    # @return [ServiceResult] the result of the operation
    def call
      case @action
      when :search
        search_products
      when :find
        find_product
      when :variants
        get_product_variants
      when :list
        list_products
      else
        failure("Invalid action: #{@action}. Supported actions: :search, :find, :variants, :list")
      end
    end

    private

    # Search for products by query with advanced filters
    # @return [ServiceResult] search results
    def search_products
      return failure("Query is required for search") if @query.blank?

      Rails.logger.info("PRODUCTS API: Searching products with query: '#{@query}', filters: #{filter_summary}")

      begin
        client = FluidClient.new
        response = client.get("/api/company/v1/products", query: search_params)

        transformed_data = transform_products_response(response)
        success(transformed_data)
      rescue => e
        Rails.logger.error("PRODUCTS API: Search failed: #{e.message}")
        failure("Failed to search products: #{e.message}")
      end
    end

    # Find a specific product by ID
    # @return [ServiceResult] product details
    def find_product
      return failure("Product ID is required") if @product_id.blank?

      Rails.logger.info("PRODUCTS API: Finding product with ID: #{@product_id}")

      begin
        client = FluidClient.new
        response = client.get("/api/company/v1/products/#{@product_id}")

        transformed_data = transform_product_response(response)
        success(transformed_data)
      rescue => e
        Rails.logger.error("PRODUCTS API: Find failed: #{e.message}")
        failure("Failed to find product: #{e.message}")
      end
    end

    # Get variants for a specific product
    # @return [ServiceResult] product variants
    def get_product_variants
      return failure("Product ID is required") if @product_id.blank?

      Rails.logger.info("Getting variants for product ID: #{@product_id}")
      
      result = get("/api/company/v1/products/#{@product_id}/variants")
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_variants_response(result.data)
      success(transformed_data)
    end

    # List all products with pagination and filters
    # @return [ServiceResult] paginated products list
    def list_products
      Rails.logger.info("PRODUCTS API: Listing products - page: #{@page}, per_page: #{@per_page}, filters: #{filter_summary}")

      begin
        client = FluidClient.new
        response = client.get("/api/company/v1/products", query: list_params)

        transformed_data = transform_products_response(response)
        success(transformed_data)
      rescue => e
        Rails.logger.error("PRODUCTS API: List failed: #{e.message}")
        failure("Failed to list products: #{e.message}")
      end
    end



    # Build search parameters with advanced filters
    # @return [Hash] search parameters
    def search_params
      params = base_params
      params[:q] = @query
      params[:search] = @query
      params
    end

    # Build list parameters with filters
    # @return [Hash] list parameters
    def list_params
      base_params
    end

    # Build base parameters common to all requests
    # @return [Hash] base parameters
    def base_params
      params = {
        page: @page,
        per_page: @per_page,
        include_variants: true,
        sort_by: @sort_by,
        sort_order: @sort_order
      }

      params[:status] = @status unless @status == 'all'
      params[:price_min] = @price_min if @price_min.present?
      params[:price_max] = @price_max if @price_max.present?
      params[:tags] = @tags.join(',') if @tags.any?

      params
    end

    # Generate filter summary for logging
    # @return [String] filter summary
    def filter_summary
      filters = []
      filters << "status: #{@status}" if @status != 'active'
      filters << "price: #{@price_min}-#{@price_max}" if @price_min || @price_max
      filters << "tags: #{@tags.join(',')}" if @tags.any?
      filters << "sort: #{@sort_by} #{@sort_order}" if @sort_by != 'name' || @sort_order != 'asc'
      filters.any? ? filters.join(', ') : 'none'
    end

    # Transform products API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_products_response(response_data)
      products = extract_products_from_response(response_data)

      {
        products: products,
        pagination: extract_pagination_from_response(response_data),
        total_count: products.length,
        filters_applied: {
          status: @status,
          price_range: [@price_min, @price_max].compact,
          tags: @tags,
          sort: "#{@sort_by} #{@sort_order}"
        }
      }
    end

    # Transform single product API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_product_response(response_data)
      product = response_data.dig("product") || response_data.dig("data") || response_data

      {
        product: normalize_product_data(product)
      }
    end

    # Transform variants API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_variants_response(response_data)
      variants = response_data.dig("variants") || response_data.dig("data") || []

      {
        variants: variants.is_a?(Array) ? variants : [],
        product_id: @product_id,
        total_variants: variants.is_a?(Array) ? variants.length : 0
      }
    end

    # Extract products array from API response
    # @param response_data [Hash] raw API response
    # @return [Array] normalized products array
    def extract_products_from_response(response_data)
      products = response_data.dig("products") ||
                response_data.dig("data", "products") ||
                response_data.dig("data") ||
                []

      return [] unless products.is_a?(Array)

      # Normalize each product
      products.map { |product| normalize_product_data(product) }
    end

    # Extract pagination info from API response
    # @param response_data [Hash] raw API response
    # @return [Hash] pagination data
    def extract_pagination_from_response(response_data)
      meta = response_data.dig("meta") || response_data.dig("pagination") || {}

      {
        current_page: meta["current_page"] || meta["page"] || @page,
        per_page: meta["per_page"] || meta["limit"] || @per_page,
        total_pages: meta["total_pages"] || meta["pages"] || 1,
        total_count: meta["total_count"] || meta["total"] || 0,
        has_next_page: meta["has_next_page"] || false,
        has_prev_page: meta["has_prev_page"] || false
      }
    end

    # Normalize product data to ensure consistent structure
    # @param product [Hash] raw product data
    # @return [Hash] normalized product data
    def normalize_product_data(product)
      return {} unless product.is_a?(Hash)

      {
        id: product["id"] || product["product_id"],
        name: product["name"] || product["title"],
        title: product["title"] || product["name"],
        sku: product["sku"],
        description: product["description"],
        price: normalize_price(product["price"]),
        compare_at_price: normalize_price(product["compare_at_price"]),
        status: product["status"] || "active",
        product_type: product["product_type"] || product["type"],
        vendor: product["vendor"],
        tags: normalize_tags(product["tags"]),
        images: normalize_images(product["images"]),
        variants: normalize_variants(product["variants"]),
        metadata: product["metadata"] || {},
        created_at: product["created_at"],
        updated_at: product["updated_at"]
      }
    end

    # Normalize price to float
    # @param price [String, Float, Integer] price value
    # @return [Float, nil] normalized price
    def normalize_price(price)
      return nil if price.blank?
      return price.to_f if price.is_a?(Numeric)
      return price.gsub(/[^\d.]/, '').to_f if price.is_a?(String)
      nil
    end

    # Normalize tags to array
    # @param tags [String, Array] tags value
    # @return [Array] normalized tags
    def normalize_tags(tags)
      return [] if tags.blank?
      return tags if tags.is_a?(Array)
      return tags.split(',').map(&:strip) if tags.is_a?(String)
      []
    end

    # Normalize images to array
    # @param images [Array, Hash] images data
    # @return [Array] normalized images
    def normalize_images(images)
      return [] if images.blank?
      return images if images.is_a?(Array)
      return [images] if images.is_a?(Hash)
      []
    end

    # Normalize variants to array
    # @param variants [Array] variants data
    # @return [Array] normalized variants
    def normalize_variants(variants)
      return [] unless variants.is_a?(Array)
      variants
    end
  end
end
